#!/usr/bin/env python3
"""
Setup script for Mac AI Agent
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"   stdout: {e.stdout}")
        if e.stderr:
            print(f"   stderr: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_conda():
    """Check if conda is available."""
    try:
        result = subprocess.run("conda --version", shell=True, 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Conda available: {result.stdout.strip()}")
            return True
    except:
        pass
    print("⚠️  Conda not found - using pip instead")
    return False


def setup_environment():
    """Set up the Python environment."""
    print("🚀 Setting up Mac AI Agent environment...\n")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check for conda
    has_conda = check_conda()
    
    # Create virtual environment if using conda
    if has_conda:
        env_name = "mac-ai-agent"
        print(f"🔧 Creating conda environment: {env_name}")
        
        # Create environment
        if not run_command(f"conda create -n {env_name} python=3.10 -y", 
                          f"Creating conda environment {env_name}"):
            return False
        
        print(f"\n✅ Environment setup complete!")
        print(f"   To activate: conda activate {env_name}")
        print(f"   Then run: pip install -r requirements.txt")
        
    else:
        # Use venv
        venv_path = Path("venv")
        if not venv_path.exists():
            if not run_command(f"{sys.executable} -m venv venv", 
                              "Creating virtual environment"):
                return False
        
        print(f"\n✅ Virtual environment setup complete!")
        print(f"   To activate: source venv/bin/activate")
        print(f"   Then run: pip install -r requirements.txt")
    
    return True


def install_dependencies():
    """Install Python dependencies."""
    print("\n🔧 Installing Python dependencies...")
    
    # Install requirements
    if not run_command("pip install -r requirements.txt", 
                      "Installing requirements"):
        return False
    
    print("✅ Dependencies installed successfully!")
    return True


def check_accessibility_permissions():
    """Check and guide user for accessibility permissions."""
    print("\n🔐 Accessibility Permissions Setup:")
    print("   For the agent to control your Mac, you need to grant accessibility permissions.")
    print("   
    print("   Steps:")
    print("   1. Go to System Preferences → Security & Privacy → Privacy → Accessibility")
    print("   2. Click the lock icon and enter your password")
    print("   3. Add your terminal app (Terminal.app or iTerm.app)")
    print("   4. Add Python (usually in /usr/bin/python3 or your conda/venv path)")
    print("   5. Make sure both are checked/enabled")
    print("   
    print("   ⚠️  Without these permissions, screen automation won't work!")


def check_lm_studio():
    """Check LM Studio setup."""
    print("\n🧠 LM Studio Setup:")
    print("   The agent uses LM Studio for local LLM intelligence.")
    print("   
    print("   Steps to set up LM Studio:")
    print("   1. Download LM Studio from: https://lmstudio.ai/")
    print("   2. Install and open LM Studio")
    print("   3. Download a model (recommended: Llama 2 7B or similar)")
    print("   4. Load the model in LM Studio")
    print("   5. Start the local server (usually runs on http://localhost:1234)")
    print("   
    print("   💡 The agent will work with limited functionality without LM Studio,")
    print("      but you'll get the best experience with a local LLM running.")


def main():
    """Main setup function."""
    print("🤖 Mac AI Agent Setup")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        print("\n❌ Environment setup failed!")
        return False
    
    # Ask if user wants to install dependencies now
    install_now = input("\n🤔 Install dependencies now? (y/n): ").lower().strip()
    
    if install_now in ['y', 'yes']:
        if not install_dependencies():
            print("\n❌ Dependency installation failed!")
            return False
    
    # Show additional setup information
    check_accessibility_permissions()
    check_lm_studio()
    
    print("\n" + "=" * 50)
    print("🎉 Setup complete!")
    print("   
    print("   Next steps:")
    print("   1. Activate your environment")
    print("   2. Install dependencies (if not done already)")
    print("   3. Set up accessibility permissions")
    print("   4. Start LM Studio with a model")
    print("   5. Run: python chat_agent.py")
    print("   
    print("   Happy automating! 🚀")
    
    return True


if __name__ == "__main__":
    main()
