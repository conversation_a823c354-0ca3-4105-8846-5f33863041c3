#!/usr/bin/env python3
"""
Test script to verify Mac AI Agent installation
"""

import sys
import importlib
from pathlib import Path


def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version.split()[0]} - Compatible")
        return True
    else:
        print(f"❌ Python {sys.version.split()[0]} - Requires 3.8+")
        return False


def test_imports():
    """Test if all required modules can be imported."""
    print("\n📦 Testing imports...")
    
    required_modules = [
        ("pyautogui", "PyAutoGUI"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("requests", "Requests"),
    ]
    
    optional_modules = [
        ("Cocoa", "PyObjC Cocoa"),
        ("Quartz", "PyObjC Quartz"),
        ("ApplicationServices", "PyObjC ApplicationServices"),
    ]
    
    all_good = True
    
    # Test required modules
    for module, name in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - Not installed")
            all_good = False
    
    # Test optional modules
    for module, name in optional_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {name} (optional)")
        except ImportError:
            print(f"⚠️  {name} (optional) - Not installed")
    
    return all_good


def test_agent_import():
    """Test if the Mac AI Agent can be imported."""
    print("\n🤖 Testing Mac AI Agent import...")
    
    try:
        from mac_ai_agent import MacAIAgent
        print("✅ MacAIAgent imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import MacAIAgent: {e}")
        return False


def test_agent_initialization():
    """Test if the agent can be initialized."""
    print("\n🚀 Testing agent initialization...")
    
    try:
        from mac_ai_agent import MacAIAgent
        agent = MacAIAgent()
        print("✅ Agent initialized successfully")
        
        # Test basic functionality
        status = agent.get_status()
        print(f"   Screen size: {status['screen_size'][0]}x{status['screen_size'][1]}")
        
        if status['lm_studio']['running']:
            print("✅ LM Studio connection detected")
        else:
            print("⚠️  LM Studio not running (optional)")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False


def test_basic_functionality():
    """Test basic agent functionality."""
    print("\n🔧 Testing basic functionality...")
    
    try:
        from mac_ai_agent import MacAIAgent
        agent = MacAIAgent()
        
        # Test screenshot capability
        print("   Testing screenshot...")
        screenshot_path = agent.take_screenshot_with_timestamp()
        if Path(screenshot_path).exists():
            print(f"✅ Screenshot saved: {screenshot_path}")
        else:
            print("❌ Screenshot failed")
            return False
        
        # Test mouse position
        print("   Testing mouse position...")
        x, y = agent.screen.get_mouse_position()
        print(f"✅ Mouse position: ({x}, {y})")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Mac AI Agent Installation Test")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Module Imports", test_imports),
        ("Agent Import", test_agent_import),
        ("Agent Initialization", test_agent_initialization),
        ("Basic Functionality", test_basic_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your installation is ready.")
        print("   Run 'python chat_agent.py' to start the agent.")
    else:
        print("\n⚠️  Some tests failed. Please check the installation:")
        print("   1. Make sure all dependencies are installed")
        print("   2. Check accessibility permissions")
        print("   3. Verify Python version (3.8+)")
        print("   4. Run 'python setup_agent.py' for guided setup")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
