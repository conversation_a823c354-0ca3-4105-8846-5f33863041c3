from setuptools import setup, find_packages

setup(
    name="mac-ai-agent",
    version="0.1.0",
    description="AI agent for Mac screen automation with clicking, dragging, screenshots, and keyboard control",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "pyautogui>=0.9.54",
        "pillow>=10.0.0",
        "opencv-python>=********",
        "pyobjc-core>=10.0",
        "pyobjc-framework-Cocoa>=10.0",
        "pyobjc-framework-Quartz>=10.0",
        "pyobjc-framework-ApplicationServices>=10.0",
        "numpy>=1.24.3",
        "pytesseract>=0.3.10",
        "click>=8.1.7",
        "pydantic>=2.4.2",
        "typing-extensions>=4.8.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.2",
            "black>=23.9.1",
        ]
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
