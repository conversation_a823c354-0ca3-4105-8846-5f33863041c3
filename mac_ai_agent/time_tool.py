"""
Time and timezone tool for Mac AI Agent
"""

import datetime
import time
import subprocess
import json
from typing import Dict, Any, Optional, List
import os

try:
    import pytz
    PYTZ_AVAILABLE = True
except ImportError:
    PYTZ_AVAILABLE = False
    print("⚠️  pytz not available - using basic timezone support")

try:
    from dateutil import tz
    DATEUTIL_AVAILABLE = True
except ImportError:
    DATEUTIL_AVAILABLE = False


class TimeTool:
    """
    Comprehensive time tool with timezone support.
    Default timezone: East Africa Time (UTC+3)
    """
    
    def __init__(self, default_timezone: str = "Africa/Nairobi"):
        """
        Initialize time tool.
        
        Args:
            default_timezone: Default timezone (East Africa Time by default)
        """
        self.default_timezone = default_timezone
        self.timezone_cache = {}
        
        # Common timezone mappings
        self.timezone_aliases = {
            "EAT": "Africa/Nairobi",  # East Africa Time
            "UTC": "UTC",
            "GMT": "GMT", 
            "EST": "America/New_York",
            "PST": "America/Los_Angeles",
            "CST": "America/Chicago",
            "MST": "America/Denver",
            "JST": "Asia/Tokyo",
            "CET": "Europe/Paris",
            "IST": "Asia/Kolkata",
            "AEST": "Australia/Sydney"
        }
    
    def get_timezone(self, timezone_name: str):
        """Get timezone object, with caching."""
        if timezone_name in self.timezone_cache:
            return self.timezone_cache[timezone_name]
        
        # Check aliases first
        actual_name = self.timezone_aliases.get(timezone_name, timezone_name)
        
        if PYTZ_AVAILABLE:
            try:
                tz_obj = pytz.timezone(actual_name)
                self.timezone_cache[timezone_name] = tz_obj
                return tz_obj
            except pytz.exceptions.UnknownTimeZoneError:
                pass
        
        if DATEUTIL_AVAILABLE:
            try:
                tz_obj = tz.gettz(actual_name)
                if tz_obj:
                    self.timezone_cache[timezone_name] = tz_obj
                    return tz_obj
            except:
                pass
        
        # Fallback to UTC offset for common timezones
        utc_offsets = {
            "EAT": 3, "UTC": 0, "GMT": 0, "EST": -5, "PST": -8,
            "CST": -6, "MST": -7, "JST": 9, "CET": 1, "IST": 5.5, "AEST": 10
        }
        
        if timezone_name in utc_offsets:
            offset_hours = utc_offsets[timezone_name]
            offset = datetime.timedelta(hours=offset_hours)
            tz_obj = datetime.timezone(offset, name=timezone_name)
            self.timezone_cache[timezone_name] = tz_obj
            return tz_obj
        
        return None
    
    def get_current_time(self, timezone: Optional[str] = None, format_str: Optional[str] = None) -> Dict[str, Any]:
        """
        Get current time in specified timezone.
        
        Args:
            timezone: Timezone name (defaults to East Africa Time)
            format_str: Custom format string
            
        Returns:
            Dictionary with time information
        """
        if timezone is None:
            timezone = self.default_timezone
        
        try:
            # Get current UTC time
            utc_now = datetime.datetime.now(datetime.timezone.utc)
            
            # Convert to target timezone
            target_tz = self.get_timezone(timezone)
            if target_tz:
                local_time = utc_now.astimezone(target_tz)
            else:
                # Fallback to system local time
                local_time = datetime.datetime.now()
                timezone = "Local"
            
            # Format time
            if format_str:
                formatted_time = local_time.strftime(format_str)
            else:
                formatted_time = local_time.strftime("%Y-%m-%d %H:%M:%S %Z")
            
            return {
                "success": True,
                "timezone": timezone,
                "datetime": local_time.isoformat(),
                "formatted": formatted_time,
                "timestamp": local_time.timestamp(),
                "date": local_time.strftime("%Y-%m-%d"),
                "time": local_time.strftime("%H:%M:%S"),
                "day_of_week": local_time.strftime("%A"),
                "month": local_time.strftime("%B"),
                "year": local_time.year,
                "hour": local_time.hour,
                "minute": local_time.minute,
                "second": local_time.second
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timezone": timezone
            }
    
    def get_time_in_multiple_zones(self, timezones: List[str]) -> Dict[str, Any]:
        """
        Get current time in multiple timezones.
        
        Args:
            timezones: List of timezone names
            
        Returns:
            Dictionary with times for each timezone
        """
        results = {}
        
        for tz in timezones:
            results[tz] = self.get_current_time(tz)
        
        return {
            "success": True,
            "times": results,
            "generated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }
    
    def get_world_clock(self) -> Dict[str, Any]:
        """Get time in major world cities."""
        major_timezones = [
            "Africa/Nairobi",    # East Africa (default)
            "UTC",               # UTC
            "America/New_York",  # Eastern US
            "America/Los_Angeles", # Pacific US
            "Europe/London",     # UK
            "Europe/Paris",      # Central Europe
            "Asia/Tokyo",        # Japan
            "Asia/Shanghai",     # China
            "Asia/Kolkata",      # India
            "Australia/Sydney"   # Australia
        ]
        
        return self.get_time_in_multiple_zones(major_timezones)
    
    def parse_time_query(self, query: str) -> Dict[str, Any]:
        """
        Parse natural language time queries.
        
        Args:
            query: Natural language query about time
            
        Returns:
            Appropriate time response
        """
        query_lower = query.lower()
        
        # Check for timezone mentions
        mentioned_tz = None
        for alias, tz_name in self.timezone_aliases.items():
            if alias.lower() in query_lower or tz_name.lower() in query_lower:
                mentioned_tz = alias
                break
        
        # Check for specific requests
        if "world" in query_lower or "global" in query_lower:
            return self.get_world_clock()
        elif "multiple" in query_lower or "different" in query_lower:
            # Default to major timezones
            return self.get_world_clock()
        elif mentioned_tz:
            return self.get_current_time(mentioned_tz)
        else:
            # Default to East Africa Time
            return self.get_current_time()
    
    def format_time_response(self, time_data: Dict[str, Any]) -> str:
        """
        Format time data into a readable response.
        
        Args:
            time_data: Time data dictionary
            
        Returns:
            Formatted string response
        """
        if not time_data.get("success"):
            return f"❌ Error getting time: {time_data.get('error', 'Unknown error')}"
        
        if "times" in time_data:
            # Multiple timezones
            response = "🌍 **World Clock:**\n"
            for tz, data in time_data["times"].items():
                if data.get("success"):
                    response += f"• **{tz}**: {data['formatted']} ({data['day_of_week']})\n"
                else:
                    response += f"• **{tz}**: Error - {data.get('error', 'Unknown')}\n"
            return response
        else:
            # Single timezone
            return (f"🕐 **Current Time ({time_data['timezone']}):**\n"
                   f"📅 {time_data['formatted']}\n"
                   f"📆 {time_data['day_of_week']}, {time_data['month']} {time_data['date'].split('-')[2]}, {time_data['year']}")
    
    def get_relative_time(self, target_time: str, timezone: Optional[str] = None) -> Dict[str, Any]:
        """
        Calculate relative time (time until/since a target time).
        
        Args:
            target_time: Target time string (e.g., "2024-01-01 12:00:00")
            timezone: Timezone for the target time
            
        Returns:
            Relative time information
        """
        try:
            # Parse target time
            if "T" in target_time:
                target_dt = datetime.datetime.fromisoformat(target_time.replace("Z", "+00:00"))
            else:
                target_dt = datetime.datetime.strptime(target_time, "%Y-%m-%d %H:%M:%S")
            
            # Add timezone if specified
            if timezone:
                target_tz = self.get_timezone(timezone)
                if target_tz:
                    target_dt = target_dt.replace(tzinfo=target_tz)
            
            # Get current time
            current_time = self.get_current_time(timezone)
            if not current_time["success"]:
                return current_time
            
            current_dt = datetime.datetime.fromisoformat(current_time["datetime"])
            
            # Calculate difference
            diff = target_dt - current_dt
            total_seconds = abs(diff.total_seconds())
            
            # Format difference
            days = int(total_seconds // 86400)
            hours = int((total_seconds % 86400) // 3600)
            minutes = int((total_seconds % 3600) // 60)
            seconds = int(total_seconds % 60)
            
            if diff.total_seconds() > 0:
                direction = "until"
            else:
                direction = "since"
            
            return {
                "success": True,
                "target_time": target_time,
                "current_time": current_time["formatted"],
                "direction": direction,
                "days": days,
                "hours": hours,
                "minutes": minutes,
                "seconds": seconds,
                "total_seconds": total_seconds,
                "formatted": f"{days}d {hours}h {minutes}m {seconds}s {direction}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "target_time": target_time
            }
    
    def check_mcp_server_time(self) -> Dict[str, Any]:
        """
        Check if mcp-server-time is available and working.
        
        Returns:
            Status of mcp-server-time
        """
        try:
            # Try to run mcp-server-time
            result = subprocess.run(
                ["python", "-m", "mcp_server_time", "--help"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "available": True,
                    "message": "mcp-server-time is available"
                }
            else:
                return {
                    "success": False,
                    "available": False,
                    "error": "mcp-server-time not working",
                    "stderr": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "available": False,
                "error": "mcp-server-time timeout"
            }
        except FileNotFoundError:
            return {
                "success": False,
                "available": False,
                "error": "mcp-server-time not installed",
                "suggestion": "Install with: pip install mcp-server-time"
            }
        except Exception as e:
            return {
                "success": False,
                "available": False,
                "error": str(e)
            }


def should_use_time_tool(user_message: str) -> bool:
    """
    Determine if a user message should trigger the time tool.
    
    Args:
        user_message: User's message
        
    Returns:
        True if should use time tool, False otherwise
    """
    time_keywords = [
        "time", "clock", "date", "today", "now", "when", "what time",
        "current time", "timezone", "hour", "minute", "morning", "afternoon",
        "evening", "night", "schedule", "calendar", "world clock"
    ]
    
    message_lower = user_message.lower()
    return any(keyword in message_lower for keyword in time_keywords)
