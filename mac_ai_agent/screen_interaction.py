"""
Core screen interaction functionality for Mac AI Agent
"""

import time
import pyautogui
from PIL import Image
import numpy as np
from typing import Tuple, Optional, Union, List
from pathlib import Path


class ScreenInteraction:
    """
    Core class for screen interaction capabilities including clicking, dragging,
    screenshots, and basic keyboard/mouse operations.
    """
    
    def __init__(self):
        """Initialize the screen interaction system."""
        # Configure PyAutoGUI for Mac
        pyautogui.FAILSAFE = True  # Move mouse to top-left corner to abort
        pyautogui.PAUSE = 0.1  # Small pause between actions
        
        # Get screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        
    def take_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """
        Take a screenshot of the screen or a specific region.
        
        Args:
            region: Optional tuple (left, top, width, height) for partial screenshot
            
        Returns:
            PIL Image object of the screenshot
        """
        if region:
            screenshot = pyautogui.screenshot(region=region)
        else:
            screenshot = pyautogui.screenshot()
        return screenshot
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> str:
        """
        Take and save a screenshot to file.
        
        Args:
            filename: Path to save the screenshot
            region: Optional region to capture
            
        Returns:
            Path to the saved screenshot
        """
        screenshot = self.take_screenshot(region)
        screenshot.save(filename)
        return filename
    
    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.0) -> None:
        """
        Click at the specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            button: Mouse button ('left', 'right', 'middle')
            clicks: Number of clicks
            interval: Interval between clicks
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
    
    def double_click(self, x: int, y: int) -> None:
        """Double-click at the specified coordinates."""
        pyautogui.doubleClick(x, y)
    
    def right_click(self, x: int, y: int) -> None:
        """Right-click at the specified coordinates."""
        pyautogui.rightClick(x, y)
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0, button: str = 'left') -> None:
        """
        Drag from start coordinates to end coordinates.
        
        Args:
            start_x: Starting X coordinate
            start_y: Starting Y coordinate
            end_x: Ending X coordinate
            end_y: Ending Y coordinate
            duration: Duration of the drag in seconds
            button: Mouse button to use for dragging
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)
    
    def move_mouse(self, x: int, y: int, duration: float = 0.0) -> None:
        """
        Move mouse to specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            duration: Duration of the movement in seconds
        """
        pyautogui.moveTo(x, y, duration=duration)
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        return pyautogui.position()
    
    def scroll(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None) -> None:
        """
        Scroll at the current mouse position or specified coordinates.
        
        Args:
            clicks: Number of scroll clicks (positive for up, negative for down)
            x: Optional X coordinate
            y: Optional Y coordinate
        """
        if x is not None and y is not None:
            pyautogui.scroll(clicks, x=x, y=y)
        else:
            pyautogui.scroll(clicks)
    
    def type_text(self, text: str, interval: float = 0.0) -> None:
        """
        Type text at the current cursor position.
        
        Args:
            text: Text to type
            interval: Interval between keystrokes
        """
        pyautogui.write(text, interval=interval)
    
    def press_key(self, key: str) -> None:
        """
        Press a single key.
        
        Args:
            key: Key name (e.g., 'enter', 'space', 'tab', 'esc')
        """
        pyautogui.press(key)
    
    def key_combo(self, *keys: str) -> None:
        """
        Press a combination of keys simultaneously.
        
        Args:
            keys: Keys to press together (e.g., 'cmd', 'c' for copy)
        """
        pyautogui.hotkey(*keys)
    
    def hold_key(self, key: str) -> None:
        """Hold down a key (use release_key to release)."""
        pyautogui.keyDown(key)
    
    def release_key(self, key: str) -> None:
        """Release a held key."""
        pyautogui.keyUp(key)
    
    def find_image_on_screen(self, image_path: str, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find an image on the screen and return its center coordinates.
        
        Args:
            image_path: Path to the image to find
            confidence: Confidence threshold (0.0 to 1.0)
            
        Returns:
            Tuple of (x, y) coordinates if found, None otherwise
        """
        try:
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                return pyautogui.center(location)
        except pyautogui.ImageNotFoundException:
            pass
        return None
    
    def click_image(self, image_path: str, confidence: float = 0.8) -> bool:
        """
        Find and click on an image on the screen.
        
        Args:
            image_path: Path to the image to find and click
            confidence: Confidence threshold
            
        Returns:
            True if image was found and clicked, False otherwise
        """
        location = self.find_image_on_screen(image_path, confidence)
        if location:
            self.click(location[0], location[1])
            return True
        return False
    
    def wait_for_image(self, image_path: str, timeout: float = 10.0, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen.
        
        Args:
            image_path: Path to the image to wait for
            timeout: Maximum time to wait in seconds
            confidence: Confidence threshold
            
        Returns:
            Tuple of (x, y) coordinates if found within timeout, None otherwise
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            location = self.find_image_on_screen(image_path, confidence)
            if location:
                return location
            time.sleep(0.5)
        return None
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Get the RGB color of a pixel at the specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            RGB tuple (r, g, b)
        """
        screenshot = self.take_screenshot()
        return screenshot.getpixel((x, y))
    
    def is_color_at_position(self, x: int, y: int, expected_color: Tuple[int, int, int], 
                           tolerance: int = 0) -> bool:
        """
        Check if a specific color exists at the given position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            expected_color: Expected RGB color tuple
            tolerance: Color tolerance (0-255)
            
        Returns:
            True if color matches within tolerance
        """
        actual_color = self.get_pixel_color(x, y)
        if tolerance == 0:
            return actual_color == expected_color
        
        return all(abs(actual - expected) <= tolerance 
                  for actual, expected in zip(actual_color, expected_color))
