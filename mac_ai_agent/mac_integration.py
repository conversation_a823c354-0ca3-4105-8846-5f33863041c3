"""
Mac-specific integration features for enhanced system control
"""

import subprocess
import time
from typing import List, Dict, Optional, Tuple
import pyautogui

try:
    import Cocoa
    import Quartz
    from ApplicationServices import AXUIElementCreateApplication, AXUIElementCopyAttributeNames
    MAC_FRAMEWORKS_AVAILABLE = True
except ImportError:
    MAC_FRAMEWORKS_AVAILABLE = False
    print("Warning: Mac frameworks not available. Some features may be limited.")


class MacIntegration:
    """
    Mac-specific system integration for enhanced automation capabilities.
    """
    
    def __init__(self):
        """Initialize Mac integration."""
        self.mac_available = MAC_FRAMEWORKS_AVAILABLE
        
    def cmd_tab_switch(self, steps: int = 1) -> None:
        """
        Use Cmd+Tab to switch between applications.
        
        Args:
            steps: Number of steps to move in the app switcher
        """
        # Hold Cmd and press Tab the specified number of times
        pyautogui.keyDown('cmd')
        for _ in range(steps):
            pyautogui.press('tab')
            time.sleep(0.1)
        pyautogui.keyUp('cmd')
    
    def cmd_backtick_switch(self, steps: int = 1) -> None:
        """
        Use Cmd+` to switch between windows of the same application.
        
        Args:
            steps: Number of steps to move between windows
        """
        for _ in range(steps):
            pyautogui.hotkey('cmd', '`')
            time.sleep(0.1)
    
    def mission_control(self) -> None:
        """Open Mission Control."""
        pyautogui.hotkey('ctrl', 'up')
    
    def show_desktop(self) -> None:
        """Show desktop (F11 or Fn+F11)."""
        pyautogui.press('f11')
    
    def spotlight_search(self, query: str = "") -> None:
        """
        Open Spotlight search and optionally type a query.
        
        Args:
            query: Optional search query to type
        """
        pyautogui.hotkey('cmd', 'space')
        time.sleep(0.5)  # Wait for Spotlight to open
        if query:
            pyautogui.write(query)
    
    def launchpad(self) -> None:
        """Open Launchpad."""
        # F4 or Fn+F4 typically opens Launchpad
        pyautogui.press('f4')
    
    def force_quit_dialog(self) -> None:
        """Open the Force Quit Applications dialog."""
        pyautogui.hotkey('cmd', 'option', 'esc')
    
    def take_screenshot_builtin(self, selection: bool = False, window: bool = False) -> None:
        """
        Use Mac's built-in screenshot functionality.
        
        Args:
            selection: Take screenshot of selection (Cmd+Shift+4)
            window: Take screenshot of window (Cmd+Shift+4, then Space)
        """
        if selection:
            pyautogui.hotkey('cmd', 'shift', '4')
        elif window:
            pyautogui.hotkey('cmd', 'shift', '4')
            time.sleep(0.5)
            pyautogui.press('space')
        else:
            # Full screen screenshot (Cmd+Shift+3)
            pyautogui.hotkey('cmd', 'shift', '3')
    
    def open_application(self, app_name: str) -> bool:
        """
        Open an application using Spotlight.
        
        Args:
            app_name: Name of the application to open
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.spotlight_search(app_name)
            time.sleep(1)
            pyautogui.press('enter')
            return True
        except Exception as e:
            print(f"Error opening application {app_name}: {e}")
            return False
    
    def get_active_application(self) -> Optional[str]:
        """
        Get the name of the currently active application.
        
        Returns:
            Name of active application or None if unavailable
        """
        if not self.mac_available:
            return None
            
        try:
            # Use AppleScript to get the active application
            script = 'tell application "System Events" to get name of first application process whose frontmost is true'
            result = subprocess.run(['osascript', '-e', script], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            print(f"Error getting active application: {e}")
        return None
    
    def get_window_list(self) -> List[Dict[str, str]]:
        """
        Get a list of all open windows.
        
        Returns:
            List of dictionaries with window information
        """
        windows = []
        if not self.mac_available:
            return windows
            
        try:
            # Use AppleScript to get window information
            script = '''
            tell application "System Events"
                set windowList to {}
                repeat with proc in application processes
                    if background only of proc is false then
                        repeat with win in windows of proc
                            set end of windowList to {name of proc, name of win}
                        end repeat
                    end if
                end repeat
                return windowList
            end tell
            '''
            result = subprocess.run(['osascript', '-e', script], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                # Parse the AppleScript output
                output = result.stdout.strip()
                # This is a simplified parser - you might need to enhance it
                for line in output.split('\n'):
                    if line.strip():
                        windows.append({"app": "Unknown", "title": line.strip()})
        except Exception as e:
            print(f"Error getting window list: {e}")
        
        return windows
    
    def activate_window(self, app_name: str, window_title: Optional[str] = None) -> bool:
        """
        Activate a specific window.
        
        Args:
            app_name: Name of the application
            window_title: Optional specific window title
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if window_title:
                script = f'''
                tell application "{app_name}"
                    activate
                    set index of window "{window_title}" to 1
                end tell
                '''
            else:
                script = f'tell application "{app_name}" to activate'
            
            result = subprocess.run(['osascript', '-e', script], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"Error activating window: {e}")
            return False
    
    def minimize_window(self) -> None:
        """Minimize the current window."""
        pyautogui.hotkey('cmd', 'm')
    
    def close_window(self) -> None:
        """Close the current window."""
        pyautogui.hotkey('cmd', 'w')
    
    def quit_application(self) -> None:
        """Quit the current application."""
        pyautogui.hotkey('cmd', 'q')
    
    def hide_application(self) -> None:
        """Hide the current application."""
        pyautogui.hotkey('cmd', 'h')
    
    def copy_to_clipboard(self) -> None:
        """Copy selection to clipboard."""
        pyautogui.hotkey('cmd', 'c')
    
    def paste_from_clipboard(self) -> None:
        """Paste from clipboard."""
        pyautogui.hotkey('cmd', 'v')
    
    def cut_to_clipboard(self) -> None:
        """Cut selection to clipboard."""
        pyautogui.hotkey('cmd', 'x')
    
    def select_all(self) -> None:
        """Select all content."""
        pyautogui.hotkey('cmd', 'a')
    
    def undo(self) -> None:
        """Undo last action."""
        pyautogui.hotkey('cmd', 'z')
    
    def redo(self) -> None:
        """Redo last undone action."""
        pyautogui.hotkey('cmd', 'shift', 'z')
    
    def save_document(self) -> None:
        """Save current document."""
        pyautogui.hotkey('cmd', 's')
    
    def open_document(self) -> None:
        """Open document dialog."""
        pyautogui.hotkey('cmd', 'o')
    
    def new_document(self) -> None:
        """Create new document."""
        pyautogui.hotkey('cmd', 'n')
    
    def find_in_document(self) -> None:
        """Open find dialog."""
        pyautogui.hotkey('cmd', 'f')
    
    def zoom_in(self) -> None:
        """Zoom in."""
        pyautogui.hotkey('cmd', '+')
    
    def zoom_out(self) -> None:
        """Zoom out."""
        pyautogui.hotkey('cmd', '-')
    
    def reset_zoom(self) -> None:
        """Reset zoom to default."""
        pyautogui.hotkey('cmd', '0')
    
    def get_system_info(self) -> Dict[str, str]:
        """
        Get basic system information.
        
        Returns:
            Dictionary with system information
        """
        info = {}
        try:
            # Get macOS version
            result = subprocess.run(['sw_vers', '-productVersion'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                info['macos_version'] = result.stdout.strip()
            
            # Get system name
            result = subprocess.run(['scutil', '--get', 'ComputerName'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                info['computer_name'] = result.stdout.strip()
                
        except Exception as e:
            print(f"Error getting system info: {e}")
        
        return info
