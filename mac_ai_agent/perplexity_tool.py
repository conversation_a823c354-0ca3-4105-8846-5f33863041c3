"""
Perplexity integration for real-time information access
"""

import subprocess
import json
import os
import tempfile
from typing import Optional, Dict, Any
import time


class PerplexityTool:
    """
    Tool for accessing real-time information via Perplexity API using npx server-perplexity-ask.
    """
    
    def __init__(self, api_key: str = "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"):
        """
        Initialize Perplexity tool.
        
        Args:
            api_key: Perplexity API key
        """
        self.api_key = api_key
        self.env = os.environ.copy()
        self.env["PERPLEXITY_API_KEY"] = api_key
        
    def check_npm_available(self) -> bool:
        """
        Check if npm/npx is available on the system.
        
        Returns:
            True if npm is available, False otherwise
        """
        try:
            result = subprocess.run(["npx", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def ask_perplexity(self, question: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Ask Perplexity a question and get real-time information.
        
        Args:
            question: Question to ask Perplexity
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with response data or None if error
        """
        if not self.check_npm_available():
            return {
                "error": "npm/npx not available. Please install Node.js and npm.",
                "question": question
            }
        
        try:
            # Use npx to run server-perplexity-ask
            cmd = ["npx", "-y", "server-perplexity-ask"]
            
            # Create input data
            input_data = {
                "question": question,
                "model": "llama-3.1-sonar-small-128k-online"  # Use online model for real-time info
            }
            
            # Run the command
            result = subprocess.run(
                cmd,
                input=json.dumps(input_data),
                capture_output=True,
                text=True,
                env=self.env,
                timeout=timeout
            )
            
            if result.returncode == 0:
                try:
                    response_data = json.loads(result.stdout)
                    return {
                        "success": True,
                        "question": question,
                        "answer": response_data.get("answer", result.stdout),
                        "sources": response_data.get("sources", []),
                        "timestamp": time.time()
                    }
                except json.JSONDecodeError:
                    # If not JSON, return raw output
                    return {
                        "success": True,
                        "question": question,
                        "answer": result.stdout.strip(),
                        "sources": [],
                        "timestamp": time.time()
                    }
            else:
                return {
                    "error": f"Perplexity command failed: {result.stderr}",
                    "question": question,
                    "returncode": result.returncode
                }
                
        except subprocess.TimeoutExpired:
            return {
                "error": f"Perplexity request timed out after {timeout} seconds",
                "question": question
            }
        except Exception as e:
            return {
                "error": f"Unexpected error: {str(e)}",
                "question": question
            }
    
    def search_current_info(self, query: str) -> Optional[str]:
        """
        Search for current information using Perplexity.
        
        Args:
            query: Search query
            
        Returns:
            Formatted response string or None if error
        """
        result = self.ask_perplexity(query)
        
        if not result:
            return "❌ Failed to get response from Perplexity"
        
        if "error" in result:
            return f"❌ Perplexity Error: {result['error']}"
        
        if result.get("success"):
            answer = result.get("answer", "No answer received")
            sources = result.get("sources", [])
            
            response = f"🔍 **Current Information:**\n{answer}"
            
            if sources:
                response += f"\n\n📚 **Sources:**"
                for i, source in enumerate(sources[:3], 1):  # Limit to 3 sources
                    if isinstance(source, dict):
                        title = source.get("title", "Unknown")
                        url = source.get("url", "")
                        response += f"\n{i}. {title}"
                        if url:
                            response += f" - {url}"
                    else:
                        response += f"\n{i}. {source}"
            
            return response
        
        return "❌ No valid response from Perplexity"
    
    def get_latest_news(self, topic: str) -> Optional[str]:
        """
        Get latest news about a specific topic.
        
        Args:
            topic: News topic to search for
            
        Returns:
            Latest news information
        """
        query = f"What are the latest news and updates about {topic}? Please provide current information from today or recent days."
        return self.search_current_info(query)
    
    def get_current_weather(self, location: str) -> Optional[str]:
        """
        Get current weather information.
        
        Args:
            location: Location to get weather for
            
        Returns:
            Current weather information
        """
        query = f"What is the current weather in {location}? Include temperature, conditions, and forecast."
        return self.search_current_info(query)
    
    def get_stock_price(self, symbol: str) -> Optional[str]:
        """
        Get current stock price information.
        
        Args:
            symbol: Stock symbol (e.g., AAPL, TSLA)
            
        Returns:
            Current stock price and information
        """
        query = f"What is the current stock price of {symbol}? Include recent price changes and market information."
        return self.search_current_info(query)
    
    def get_tech_updates(self, topic: str) -> Optional[str]:
        """
        Get latest technology updates.
        
        Args:
            topic: Technology topic (e.g., "AI", "Apple", "Tesla")
            
        Returns:
            Latest tech updates
        """
        query = f"What are the latest technology updates and news about {topic}? Include recent developments and announcements."
        return self.search_current_info(query)
    
    def answer_current_question(self, question: str) -> Optional[str]:
        """
        Answer any question that might require current information.
        
        Args:
            question: Question that might need real-time data
            
        Returns:
            Answer with current information
        """
        # Add context to ensure we get current information
        enhanced_query = f"{question} Please provide the most current and up-to-date information available."
        return self.search_current_info(enhanced_query)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the Perplexity connection.
        
        Returns:
            Test result dictionary
        """
        if not self.check_npm_available():
            return {
                "success": False,
                "error": "npm/npx not available",
                "suggestion": "Install Node.js and npm to use Perplexity integration"
            }
        
        # Test with a simple question
        test_result = self.ask_perplexity("What is today's date?")
        
        if test_result and test_result.get("success"):
            return {
                "success": True,
                "message": "Perplexity connection working",
                "test_response": test_result.get("answer", "")[:100] + "..."
            }
        else:
            return {
                "success": False,
                "error": test_result.get("error", "Unknown error") if test_result else "No response",
                "suggestion": "Check your Perplexity API key and internet connection"
            }


# Utility functions for integration with the main agent
def create_perplexity_prompts():
    """Create specialized prompts for Perplexity integration."""

    PERPLEXITY_SYSTEM_PROMPT = """You are an AI assistant with access to real-time information through Perplexity. When a user asks questions that might require current information (news, weather, stock prices, recent events, etc.), you should identify if the question needs real-time data, use the Perplexity tool to get current information, combine the real-time data with your knowledge, and provide a comprehensive answer.

Examples of questions that need real-time data:
- "What's the weather like today?"
- "What's the latest news about AI?"
- "What's Apple's stock price?"
- "What happened recently with Tesla?"
- "What are the current events?"

For Mac automation tasks, continue to use your existing capabilities."""

    return PERPLEXITY_SYSTEM_PROMPT


def should_use_perplexity(user_message: str) -> bool:
    """
    Determine if a user message should trigger Perplexity search.
    
    Args:
        user_message: User's message
        
    Returns:
        True if should use Perplexity, False otherwise
    """
    current_info_keywords = [
        "current", "latest", "recent", "today", "now", "weather", "news", 
        "stock", "price", "happening", "update", "what's", "events",
        "market", "trending", "breaking", "live", "real-time"
    ]
    
    message_lower = user_message.lower()
    return any(keyword in message_lower for keyword in current_info_keywords)
