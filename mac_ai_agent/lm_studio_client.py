"""
LM Studio client for local LLM integration
"""

import requests
import json
from typing import Dict, List, Optional, Any
import time


class LMStudioClient:
    """
    Client for communicating with LM Studio's local API server.
    LM Studio provides an OpenAI-compatible API when running a local server.
    """
    
    def __init__(self, base_url: str = "http://127.0.0.1:1234", api_key: str = "lm-studio"):
        """
        Initialize LM Studio client.
        
        Args:
            base_url: Base URL for LM Studio server (default: http://127.0.0.1:1234)
            api_key: API key (LM Studio uses "lm-studio" by default)
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
    def is_server_running(self) -> bool:
        """
        Check if LM Studio server is running and accessible.
        
        Returns:
            True if server is accessible, False otherwise
        """
        try:
            response = requests.get(f"{self.base_url}/v1/models", 
                                  headers=self.headers, timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available models from LM Studio.
        
        Returns:
            List of model information dictionaries
        """
        try:
            response = requests.get(f"{self.base_url}/v1/models", 
                                  headers=self.headers, timeout=10)
            if response.status_code == 200:
                return response.json().get("data", [])
        except requests.exceptions.RequestException as e:
            print(f"Error getting models: {e}")
        return []
    
    def chat_completion(self, messages: List[Dict[str, str]], 
                       model: Optional[str] = None,
                       temperature: float = 0.7,
                       max_tokens: int = 1000,
                       stream: bool = False) -> Optional[Dict[str, Any]]:
        """
        Send a chat completion request to LM Studio.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model name (if None, uses the loaded model)
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            
        Returns:
            Response dictionary or None if error
        """
        payload = {
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        
        # Add model if specified
        if model:
            payload["model"] = model
        
        try:
            response = requests.post(f"{self.base_url}/v1/chat/completions",
                                   headers=self.headers,
                                   json=payload,
                                   timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Error making request: {e}")
            return None
    
    def simple_chat(self, user_message: str, system_prompt: Optional[str] = None) -> Optional[str]:
        """
        Simple chat interface for single message exchanges.
        Compatible with vision models like Llava that may not support system role.

        Args:
            user_message: User's message
            system_prompt: Optional system prompt to set context

        Returns:
            AI response text or None if error
        """
        messages = []

        # For vision models, combine system prompt with user message
        if system_prompt:
            combined_message = f"{system_prompt}\n\nUser: {user_message}"
            messages.append({"role": "user", "content": combined_message})
        else:
            messages.append({"role": "user", "content": user_message})

        response = self.chat_completion(messages)

        if response and "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]

        return None
    
    def get_server_status(self) -> Dict[str, Any]:
        """
        Get detailed server status information.
        
        Returns:
            Dictionary with server status information
        """
        status = {
            "running": False,
            "models": [],
            "base_url": self.base_url
        }
        
        if self.is_server_running():
            status["running"] = True
            status["models"] = self.get_available_models()
        
        return status


class MacAutomationPrompts:
    """
    Specialized prompts for Mac automation tasks.
    """
    
    SYSTEM_PROMPT = """You are an AI assistant that controls a Mac computer through automation. You can take screenshots, analyze what's on screen, click on UI elements, type text, use keyboard shortcuts, open applications using Spotlight search, use Mac-specific shortcuts like Cmd+Tab to switch apps, drag and drop items, and scroll and navigate interfaces.

When a user asks you to do something, break it down into specific automation steps. Always be precise about coordinates, application names, and actions.

Examples:
- "Open Safari" → Use spotlight_search("Safari") then press Enter
- "Click the close button" → click(x, y) where x,y are the close button coordinates
- "Switch to the next app" → Use cmd_tab_switch()
- "Take a screenshot" → Use take_screenshot()

Always confirm what you see on screen before taking actions. Be helpful but cautious with automation commands. Respond with clear, actionable steps and explain what you're going to do before doing it."""

    @staticmethod
    def create_action_prompt(user_request: str, screen_context: str = "") -> str:
        """
        Create a prompt for action planning based on user request and screen context.
        
        Args:
            user_request: What the user wants to do
            screen_context: Description of current screen state
            
        Returns:
            Formatted prompt for the LLM
        """
        prompt = f"""User Request: {user_request}

Current Screen Context: {screen_context}

Please provide a step-by-step plan to accomplish this request. Format your response as:

PLAN:
1. [First action with specific details]
2. [Second action with specific details]
...

EXPLANATION:
[Brief explanation of why these steps will accomplish the goal]

Be specific about coordinates, application names, and exact actions needed."""
        
        return prompt
    
    @staticmethod
    def create_screen_analysis_prompt(user_question: str) -> str:
        """
        Create a prompt for analyzing screenshot content.
        
        Args:
            user_question: Question about what's on screen
            
        Returns:
            Formatted prompt for screen analysis
        """
        return f"""Analyze the current screenshot and answer this question: {user_question}

Please describe:
1. What applications/windows are visible
2. Key UI elements and their approximate locations
3. Any text or content relevant to the question
4. Suggested actions if applicable

Be specific and helpful in your analysis."""
