"""
Main AI Agent class that combines all capabilities
"""

import time
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from pathlib import Path

from .screen_interaction import ScreenInteraction
from .lm_studio_client import LMStudioClient, MacAutomationPrompts
from .perplexity_tool import PerplexityTool, should_use_perplexity, create_perplexity_prompts
from .time_tool import TimeTool, should_use_time_tool


class MacAIAgent:
    """
    Main AI Agent class that combines screen automation with local LLM intelligence.
    """
    
    def __init__(self, lm_studio_url: str = "http://127.0.0.1:1234"):
        """
        Initialize the Mac AI Agent.

        Args:
            lm_studio_url: URL for LM Studio server
        """
        self.screen = ScreenInteraction()
        self.llm = LMStudioClient(base_url=lm_studio_url)
        self.perplexity = PerplexityTool()
        self.time_tool = TimeTool()  # Default to East Africa Time (UTC+3)
        self.conversation_history: List[Dict[str, str]] = []
        self.screenshots_dir = Path("screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)

        # Check if LM Studio is running
        if not self.llm.is_server_running():
            print("⚠️  Warning: LM Studio server not detected at", lm_studio_url)
            print("   Please make sure LM Studio is running with a model loaded.")
            print("   Start LM Studio → Load a model → Start Server")

        # Test Perplexity connection
        perplexity_status = self.perplexity.test_connection()
        if perplexity_status["success"]:
            print("✅ Perplexity integration ready for real-time information")
        else:
            print(f"⚠️  Perplexity integration limited: {perplexity_status['error']}")
            if "suggestion" in perplexity_status:
                print(f"   {perplexity_status['suggestion']}")

        # Test time tool
        current_time = self.time_tool.get_current_time()
        if current_time["success"]:
            print(f"✅ Time tool ready - Current time: {current_time['formatted']}")
        else:
            print(f"⚠️  Time tool limited: {current_time.get('error', 'Unknown error')}")
    
    def take_screenshot_with_timestamp(self) -> str:
        """
        Take a screenshot and save it with timestamp.
        
        Returns:
            Path to the saved screenshot
        """
        timestamp = int(time.time())
        filename = self.screenshots_dir / f"screenshot_{timestamp}.png"
        return self.screen.save_screenshot(str(filename))
    
    def analyze_screen(self, question: str = "What do you see on the screen?") -> Optional[str]:
        """
        Take a screenshot and ask the LLM to analyze it.
        
        Args:
            question: Question to ask about the screen
            
        Returns:
            LLM's analysis of the screen
        """
        screenshot_path = self.take_screenshot_with_timestamp()
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        # For now, we'll ask the LLM to analyze based on the question
        # In a more advanced version, we could use vision models
        prompt = MacAutomationPrompts.create_screen_analysis_prompt(question)
        
        response = self.llm.simple_chat(
            user_message=prompt,
            system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
        )
        
        return response
    
    def plan_action(self, user_request: str, screen_context: str = "") -> Optional[str]:
        """
        Ask the LLM to create an action plan for the user's request.
        
        Args:
            user_request: What the user wants to do
            screen_context: Current screen context
            
        Returns:
            Action plan from the LLM
        """
        prompt = MacAutomationPrompts.create_action_prompt(user_request, screen_context)
        
        response = self.llm.simple_chat(
            user_message=prompt,
            system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
        )
        
        return response
    
    def execute_action(self, action_type: str, **kwargs) -> bool:
        """
        Execute a specific automation action.
        
        Args:
            action_type: Type of action to execute
            **kwargs: Action parameters
            
        Returns:
            True if action was executed successfully
        """
        try:
            if action_type == "click":
                x, y = kwargs.get("x", 0), kwargs.get("y", 0)
                self.screen.click(x, y)
                print(f"🖱️  Clicked at ({x}, {y})")
                
            elif action_type == "type":
                text = kwargs.get("text", "")
                self.screen.type_text(text)
                print(f"⌨️  Typed: {text}")
                
            elif action_type == "key_combo":
                keys = kwargs.get("keys", [])
                self.screen.key_combo(*keys)
                print(f"⌨️  Key combo: {' + '.join(keys)}")
                
            elif action_type == "spotlight_search":
                query = kwargs.get("query", "")
                self.spotlight_search(query)
                print(f"🔍 Spotlight search: {query}")
                
            elif action_type == "cmd_tab":
                steps = kwargs.get("steps", 1)
                self.cmd_tab_switch(steps)
                print(f"🔄 Cmd+Tab switch ({steps} steps)")
                
            elif action_type == "screenshot":
                path = self.take_screenshot_with_timestamp()
                print(f"📸 Screenshot: {path}")
                
            elif action_type == "drag":
                start_x = kwargs.get("start_x", 0)
                start_y = kwargs.get("start_y", 0)
                end_x = kwargs.get("end_x", 0)
                end_y = kwargs.get("end_y", 0)
                duration = kwargs.get("duration", 1.0)
                self.screen.drag(start_x, start_y, end_x, end_y, duration)
                print(f"🖱️  Dragged from ({start_x}, {start_y}) to ({end_x}, {end_y})")
                
            else:
                print(f"❌ Unknown action type: {action_type}")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ Error executing action {action_type}: {e}")
            return False
    
    def spotlight_search(self, query: str) -> None:
        """Open Spotlight and search for something."""
        self.screen.key_combo("cmd", "space")
        time.sleep(0.5)
        if query:
            self.screen.type_text(query)
    
    def cmd_tab_switch(self, steps: int = 1) -> None:
        """Use Cmd+Tab to switch applications."""
        self.screen.key_combo("cmd", "tab")
        for _ in range(steps - 1):
            time.sleep(0.1)
            self.screen.press_key("tab")
    
    def chat(self, user_message: str) -> str:
        """
        Main chat interface that processes user messages and can take actions.

        Args:
            user_message: User's message/request

        Returns:
            Agent's response
        """
        # Add user message to conversation history
        self.conversation_history.append({"role": "user", "content": user_message})

        # Check what type of request this is
        needs_current_info = should_use_perplexity(user_message)
        needs_time_info = should_use_time_tool(user_message)

        # Check if this is an action request
        action_keywords = [
            "click", "open", "search", "type", "screenshot", "switch",
            "drag", "close", "minimize", "find", "scroll"
        ]

        is_action_request = any(keyword in user_message.lower() for keyword in action_keywords)

        # Handle time-related requests
        if needs_time_info and not is_action_request:
            print("🕐 Getting time information...")
            time_data = self.time_tool.parse_time_query(user_message)
            time_response = self.time_tool.format_time_response(time_data)

            if time_response:
                # Combine time info with LLM response for context
                enhanced_prompt = f"""User asked: {user_message}

Current time information:
{time_response}

Please provide a helpful response incorporating this time information. Be conversational and helpful."""

                response = self.llm.simple_chat(
                    user_message=enhanced_prompt,
                    system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
                )

                if response:
                    response = f"🕐 **[Time Info]** {response}"
                else:
                    response = time_response
            else:
                response = "❌ Sorry, I couldn't get time information."

        # Handle real-time information requests
        elif needs_current_info and not is_action_request:
            print("🔍 Getting real-time information from Perplexity...")
            perplexity_response = self.perplexity.answer_current_question(user_message)

            if perplexity_response:
                # Combine Perplexity info with LLM response
                enhanced_prompt = f"""User asked: {user_message}

Real-time information from Perplexity:
{perplexity_response}

Please provide a comprehensive answer combining this real-time information with your knowledge. Be helpful and informative."""

                response = self.llm.simple_chat(
                    user_message=enhanced_prompt,
                    system_prompt=create_perplexity_prompts()
                )

                if response:
                    # Add real-time info indicator
                    response = f"🌐 **[Real-time Info]** {response}"
                else:
                    response = perplexity_response
            else:
                response = "❌ Sorry, I couldn't get current information. Let me try to help with what I know."

        elif is_action_request:
            # Take a screenshot for context
            screenshot_path = self.take_screenshot_with_timestamp()
            screen_context = f"Screenshot taken and saved to {screenshot_path}"
            
            # Get action plan from LLM
            plan = self.plan_action(user_message, screen_context)
            
            if plan:
                print("\n🤖 AI Agent Plan:")
                print(plan)
                print("\n" + "="*50)
                
                # Ask user if they want to proceed
                proceed = input("\n🤔 Should I execute this plan? (y/n): ").lower().strip()
                
                if proceed == 'y' or proceed == 'yes':
                    # For now, we'll let the user manually execute based on the plan
                    # In a more advanced version, we could parse the plan and auto-execute
                    response = "I've provided the action plan above. You can execute it step by step, or I can help you with specific actions. Just tell me what to do next!"
                else:
                    response = "Okay, I won't execute the plan. Let me know if you'd like me to suggest a different approach."
            else:
                response = "I couldn't create a plan for that request. Could you be more specific about what you'd like me to do?"
        else:
            # Regular conversation
            response = self.llm.simple_chat(
                user_message=user_message,
                system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
            )
            
            if not response:
                response = "I'm having trouble connecting to the local LLM. Please make sure LM Studio is running with a model loaded."
        
        # Add response to conversation history
        if response:
            self.conversation_history.append({"role": "assistant", "content": response})
        
        return response or "Sorry, I couldn't process that request."
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the agent and its components.

        Returns:
            Status dictionary
        """
        return {
            "screen_size": (self.screen.screen_width, self.screen.screen_height),
            "lm_studio": self.llm.get_server_status(),
            "perplexity": self.perplexity.test_connection(),
            "time_tool": self.time_tool.get_current_time(),
            "conversation_length": len(self.conversation_history),
            "screenshots_dir": str(self.screenshots_dir)
        }
    
    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
        print("🗑️  Conversation history cleared.")
    
    def save_conversation(self, filename: str) -> None:
        """Save conversation history to a file."""
        with open(filename, 'w') as f:
            json.dump(self.conversation_history, f, indent=2)
        print(f"💾 Conversation saved to {filename}")
    
    def load_conversation(self, filename: str) -> None:
        """Load conversation history from a file."""
        try:
            with open(filename, 'r') as f:
                self.conversation_history = json.load(f)
            print(f"📂 Conversation loaded from {filename}")
        except FileNotFoundError:
            print(f"❌ File not found: {filename}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {filename}")
