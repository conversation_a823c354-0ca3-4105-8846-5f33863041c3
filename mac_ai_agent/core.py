"""
Main AI Agent class that combines all capabilities
"""

import time
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from pathlib import Path

from .screen_interaction import ScreenInteraction
from .lm_studio_client import LMStudioClient, MacAutomationPrompts
from .perplexity_tool import PerplexityTool, should_use_perplexity, create_perplexity_prompts
from .time_tool import TimeTool, should_use_time_tool


class MacAIAgent:
    """
    Main AI Agent class that combines screen automation with local LLM intelligence.
    """
    
    def __init__(self, lm_studio_url: str = "http://127.0.0.1:1234"):
        """
        Initialize the Mac AI Agent.

        Args:
            lm_studio_url: URL for LM Studio server
        """
        self.screen = ScreenInteraction()
        self.llm = LMStudioClient(base_url=lm_studio_url)
        self.perplexity = PerplexityTool()
        self.time_tool = TimeTool()  # Default to East Africa Time (UTC+3)
        self.conversation_history: List[Dict[str, str]] = []
        self.screenshots_dir = Path("screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)

        # Check if LM Studio is running
        if not self.llm.is_server_running():
            print("⚠️  Warning: LM Studio server not detected at", lm_studio_url)
            print("   Please make sure LM Studio is running with a model loaded.")
            print("   Start LM Studio → Load a model → Start Server")

        # Test Perplexity connection
        perplexity_status = self.perplexity.test_connection()
        if perplexity_status["success"]:
            print("✅ Perplexity integration ready for real-time information")
        else:
            print(f"⚠️  Perplexity integration limited: {perplexity_status['error']}")
            if "suggestion" in perplexity_status:
                print(f"   {perplexity_status['suggestion']}")

        # Test time tool
        current_time = self.time_tool.get_current_time()
        if current_time["success"]:
            print(f"✅ Time tool ready - Current time: {current_time['formatted']}")
        else:
            print(f"⚠️  Time tool limited: {current_time.get('error', 'Unknown error')}")
    
    def take_screenshot_with_timestamp(self) -> str:
        """
        Take a screenshot and save it with timestamp.
        
        Returns:
            Path to the saved screenshot
        """
        timestamp = int(time.time())
        filename = self.screenshots_dir / f"screenshot_{timestamp}.png"
        return self.screen.save_screenshot(str(filename))
    
    def analyze_screen(self, question: str = "What do you see on the screen?") -> Optional[str]:
        """
        Take a screenshot and ask the LLM to analyze it.
        
        Args:
            question: Question to ask about the screen
            
        Returns:
            LLM's analysis of the screen
        """
        screenshot_path = self.take_screenshot_with_timestamp()
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        # For now, we'll ask the LLM to analyze based on the question
        # In a more advanced version, we could use vision models
        prompt = MacAutomationPrompts.create_screen_analysis_prompt(question)
        
        response = self.llm.simple_chat(
            user_message=prompt,
            system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
        )
        
        return response
    
    def plan_action(self, user_request: str, screen_context: str = "") -> Optional[str]:
        """
        Ask the LLM to create an action plan for the user's request.
        
        Args:
            user_request: What the user wants to do
            screen_context: Current screen context
            
        Returns:
            Action plan from the LLM
        """
        prompt = MacAutomationPrompts.create_action_prompt(user_request, screen_context)
        
        response = self.llm.simple_chat(
            user_message=prompt,
            system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
        )
        
        return response
    
    def execute_action(self, action_type: str, **kwargs) -> bool:
        """
        Execute a specific automation action.
        
        Args:
            action_type: Type of action to execute
            **kwargs: Action parameters
            
        Returns:
            True if action was executed successfully
        """
        try:
            if action_type == "click":
                x, y = kwargs.get("x", 0), kwargs.get("y", 0)
                self.screen.click(x, y)
                print(f"🖱️  Clicked at ({x}, {y})")
                
            elif action_type == "type":
                text = kwargs.get("text", "")
                self.screen.type_text(text)
                print(f"⌨️  Typed: {text}")
                
            elif action_type == "key_combo":
                keys = kwargs.get("keys", [])
                self.screen.key_combo(*keys)
                print(f"⌨️  Key combo: {' + '.join(keys)}")
                
            elif action_type == "spotlight_search":
                query = kwargs.get("query", "")
                self.spotlight_search(query)
                print(f"🔍 Spotlight search: {query}")
                
            elif action_type == "cmd_tab":
                steps = kwargs.get("steps", 1)
                self.cmd_tab_switch(steps)
                print(f"🔄 Cmd+Tab switch ({steps} steps)")
                
            elif action_type == "screenshot":
                path = self.take_screenshot_with_timestamp()
                print(f"📸 Screenshot: {path}")
                
            elif action_type == "drag":
                start_x = kwargs.get("start_x", 0)
                start_y = kwargs.get("start_y", 0)
                end_x = kwargs.get("end_x", 0)
                end_y = kwargs.get("end_y", 0)
                duration = kwargs.get("duration", 1.0)
                self.screen.drag(start_x, start_y, end_x, end_y, duration)
                print(f"🖱️  Dragged from ({start_x}, {start_y}) to ({end_x}, {end_y})")
                
            else:
                print(f"❌ Unknown action type: {action_type}")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ Error executing action {action_type}: {e}")
            return False
    
    def spotlight_search(self, query: str) -> None:
        """Open Spotlight and search for something."""
        self.screen.key_combo("cmd", "space")
        time.sleep(0.5)
        if query:
            self.screen.type_text(query)
    
    def cmd_tab_switch(self, steps: int = 1) -> None:
        """Use Cmd+Tab to switch applications."""
        self.screen.key_combo("cmd", "tab")
        for _ in range(steps - 1):
            time.sleep(0.1)
            self.screen.press_key("tab")

    def open_application(self, app_name: str) -> bool:
        """
        Open an application using Spotlight search.

        Args:
            app_name: Name of the application to open

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"🔍 Opening {app_name} using Spotlight...")

            # Open Spotlight
            self.spotlight_search(app_name)
            time.sleep(1.5)  # Wait for Spotlight to show results

            # Press Enter to open the first result
            self.screen.press_key("enter")
            time.sleep(2)  # Wait for app to start launching

            print(f"✅ {app_name} should be opening...")
            return True

        except Exception as e:
            print(f"❌ Error opening {app_name}: {e}")
            return False

    def execute_simple_action(self, user_message: str) -> Optional[Dict[str, Any]]:
        """
        Execute simple actions directly without LLM planning.

        Args:
            user_message: User's action request

        Returns:
            Dictionary with execution result or None if not a simple action
        """
        message_lower = user_message.lower().strip()

        # Handle "open [app]" requests
        if message_lower.startswith("open "):
            app_name = message_lower[5:].strip()

            # Common app name mappings
            app_mappings = {
                "chrome": "Google Chrome",
                "safari": "Safari",
                "firefox": "Firefox",
                "calculator": "Calculator",
                "notes": "Notes",
                "mail": "Mail",
                "calendar": "Calendar",
                "finder": "Finder",
                "terminal": "Terminal",
                "vscode": "Visual Studio Code",
                "code": "Visual Studio Code",
                "spotify": "Spotify",
                "slack": "Slack",
                "discord": "Discord",
                "zoom": "Zoom",
                "teams": "Microsoft Teams",
                "word": "Microsoft Word",
                "excel": "Microsoft Excel",
                "powerpoint": "Microsoft PowerPoint"
            }

            # Get the proper app name
            proper_app_name = app_mappings.get(app_name, app_name.title())

            success = self.open_application(proper_app_name)

            return {
                "action": "open_app",
                "app_name": proper_app_name,
                "success": success,
                "message": f"{'Successfully opened' if success else 'Failed to open'} {proper_app_name}"
            }

        # Handle "take screenshot" requests
        elif "screenshot" in message_lower:
            try:
                screenshot_path = self.take_screenshot_with_timestamp()
                return {
                    "action": "screenshot",
                    "success": True,
                    "path": screenshot_path,
                    "message": f"Screenshot saved to {screenshot_path}"
                }
            except Exception as e:
                return {
                    "action": "screenshot",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to take screenshot: {e}"
                }

        # Handle "switch app" or "cmd+tab" requests
        elif any(phrase in message_lower for phrase in ["switch app", "cmd+tab", "cmd tab", "next app"]):
            try:
                self.cmd_tab_switch()
                return {
                    "action": "switch_app",
                    "success": True,
                    "message": "Switched to next application using Cmd+Tab"
                }
            except Exception as e:
                return {
                    "action": "switch_app",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to switch apps: {e}"
                }

        # If no direct action found, return None to use LLM planning
        return None

    def open_application(self, app_name: str) -> bool:
        """
        Open an application using Spotlight search.

        Args:
            app_name: Name of the application to open

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"🔍 Opening {app_name} using Spotlight...")

            # Open Spotlight
            self.spotlight_search(app_name)
            time.sleep(1.5)  # Wait for Spotlight to show results

            # Press Enter to open the first result
            self.screen.press_key("enter")
            time.sleep(2)  # Wait for app to start launching

            print(f"✅ {app_name} should be opening...")
            return True

        except Exception as e:
            print(f"❌ Error opening {app_name}: {e}")
            return False

    def parse_and_execute_action(self, user_message: str) -> Dict[str, Any]:
        """
        Parse user message and execute simple actions directly.

        Args:
            user_message: User's action request

        Returns:
            Dictionary with execution result
        """
        message_lower = user_message.lower().strip()

        # Handle "open [app]" requests
        if message_lower.startswith("open "):
            app_name = message_lower[5:].strip()

            # Common app name mappings
            app_mappings = {
                "chrome": "Google Chrome",
                "safari": "Safari",
                "firefox": "Firefox",
                "calculator": "Calculator",
                "notes": "Notes",
                "mail": "Mail",
                "calendar": "Calendar",
                "finder": "Finder",
                "terminal": "Terminal",
                "vscode": "Visual Studio Code",
                "code": "Visual Studio Code",
                "spotify": "Spotify",
                "slack": "Slack",
                "discord": "Discord",
                "zoom": "Zoom",
                "teams": "Microsoft Teams",
                "word": "Microsoft Word",
                "excel": "Microsoft Excel",
                "powerpoint": "Microsoft PowerPoint",
                "photoshop": "Adobe Photoshop",
                "illustrator": "Adobe Illustrator",
                "xcode": "Xcode"
            }

            # Get the proper app name
            proper_app_name = app_mappings.get(app_name, app_name.title())

            success = self.open_application(proper_app_name)

            return {
                "action": "open_app",
                "app_name": proper_app_name,
                "success": success,
                "message": f"{'Successfully opened' if success else 'Failed to open'} {proper_app_name}"
            }

        # Handle "take screenshot" requests
        elif "screenshot" in message_lower:
            try:
                screenshot_path = self.take_screenshot_with_timestamp()
                return {
                    "action": "screenshot",
                    "success": True,
                    "path": screenshot_path,
                    "message": f"Screenshot saved to {screenshot_path}"
                }
            except Exception as e:
                return {
                    "action": "screenshot",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to take screenshot: {e}"
                }

        # Handle "switch app" or "cmd+tab" requests
        elif any(phrase in message_lower for phrase in ["switch app", "cmd+tab", "cmd tab", "next app"]):
            try:
                self.cmd_tab_switch()
                return {
                    "action": "switch_app",
                    "success": True,
                    "message": "Switched to next application using Cmd+Tab"
                }
            except Exception as e:
                return {
                    "action": "switch_app",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to switch apps: {e}"
                }

        # Handle typing requests
        elif message_lower.startswith("type "):
            text_to_type = user_message[5:].strip()
            try:
                self.screen.type_text(text_to_type)
                return {
                    "action": "type",
                    "text": text_to_type,
                    "success": True,
                    "message": f"Typed: {text_to_type}"
                }
            except Exception as e:
                return {
                    "action": "type",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to type text: {e}"
                }

        # Handle click requests
        elif "click" in message_lower and any(char.isdigit() for char in message_lower):
            try:
                # Extract coordinates from message
                import re
                numbers = re.findall(r'\d+', message_lower)
                if len(numbers) >= 2:
                    x, y = int(numbers[0]), int(numbers[1])
                    self.screen.click(x, y)
                    return {
                        "action": "click",
                        "coordinates": (x, y),
                        "success": True,
                        "message": f"Clicked at ({x}, {y})"
                    }
                else:
                    return {
                        "action": "click",
                        "success": False,
                        "message": "Could not extract coordinates from message"
                    }
            except Exception as e:
                return {
                    "action": "click",
                    "success": False,
                    "error": str(e),
                    "message": f"Failed to click: {e}"
                }

        # If no direct action found, return None to use LLM planning
        return None
    
    def chat(self, user_message: str) -> str:
        """
        Main chat interface that processes user messages and can take actions.

        Args:
            user_message: User's message/request

        Returns:
            Agent's response
        """
        # Add user message to conversation history
        self.conversation_history.append({"role": "user", "content": user_message})

        # Check what type of request this is
        needs_current_info = should_use_perplexity(user_message)
        needs_time_info = should_use_time_tool(user_message)

        # Check if this is an action request
        action_keywords = [
            "click", "open", "search", "type", "screenshot", "switch",
            "drag", "close", "minimize", "find", "scroll"
        ]

        is_action_request = any(keyword in user_message.lower() for keyword in action_keywords)

        # Handle time-related requests
        if needs_time_info and not is_action_request:
            print("🕐 Getting time information...")
            time_data = self.time_tool.parse_time_query(user_message)
            time_response = self.time_tool.format_time_response(time_data)

            if time_response:
                # Combine time info with LLM response for context
                enhanced_prompt = f"""User asked: {user_message}

Current time information:
{time_response}

Please provide a helpful response incorporating this time information. Be conversational and helpful."""

                response = self.llm.simple_chat(
                    user_message=enhanced_prompt,
                    system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
                )

                if response:
                    response = f"🕐 **[Time Info]** {response}"
                else:
                    response = time_response
            else:
                response = "❌ Sorry, I couldn't get time information."

        # Handle real-time information requests
        elif needs_current_info and not is_action_request:
            print("🔍 Getting real-time information from Perplexity...")
            perplexity_response = self.perplexity.answer_current_question(user_message)

            if perplexity_response:
                # Combine Perplexity info with LLM response
                enhanced_prompt = f"""User asked: {user_message}

Real-time information from Perplexity:
{perplexity_response}

Please provide a comprehensive answer combining this real-time information with your knowledge. Be helpful and informative."""

                response = self.llm.simple_chat(
                    user_message=enhanced_prompt,
                    system_prompt=create_perplexity_prompts()
                )

                if response:
                    # Add real-time info indicator
                    response = f"🌐 **[Real-time Info]** {response}"
                else:
                    response = perplexity_response
            else:
                response = "❌ Sorry, I couldn't get current information. Let me try to help with what I know."

        elif is_action_request:
            # Try to execute simple actions directly first
            direct_result = self.execute_simple_action(user_message)

            if direct_result:
                # Direct execution successful
                if direct_result["success"]:
                    response = f"✅ {direct_result['message']}"
                else:
                    response = f"❌ {direct_result['message']}"
            else:
                # Fall back to LLM planning for complex actions
                screenshot_path = self.take_screenshot_with_timestamp()
                screen_context = f"Screenshot taken and saved to {screenshot_path}"

                plan = self.plan_action(user_message, screen_context)

                if plan:
                    print("\n🤖 AI Agent Plan:")
                    print(plan)
                    print("\n" + "="*50)

                    proceed = input("\n🤔 Should I execute this plan? (y/n): ").lower().strip()

                    if proceed == 'y' or proceed == 'yes':
                        response = "I've provided the action plan above. You can execute it step by step, or I can help you with specific actions. Just tell me what to do next!"
                    else:
                        response = "Okay, I won't execute the plan. Let me know if you'd like me to suggest a different approach."
                else:
                    response = "I couldn't create a plan for that request. Could you be more specific about what you'd like me to do?"
        else:
            # Regular conversation
            response = self.llm.simple_chat(
                user_message=user_message,
                system_prompt=MacAutomationPrompts.SYSTEM_PROMPT
            )
            
            if not response:
                response = "I'm having trouble connecting to the local LLM. Please make sure LM Studio is running with a model loaded."
        
        # Add response to conversation history
        if response:
            self.conversation_history.append({"role": "assistant", "content": response})
        
        return response or "Sorry, I couldn't process that request."
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the agent and its components.

        Returns:
            Status dictionary
        """
        return {
            "screen_size": (self.screen.screen_width, self.screen.screen_height),
            "lm_studio": self.llm.get_server_status(),
            "perplexity": self.perplexity.test_connection(),
            "time_tool": self.time_tool.get_current_time(),
            "conversation_length": len(self.conversation_history),
            "screenshots_dir": str(self.screenshots_dir)
        }
    
    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
        print("🗑️  Conversation history cleared.")
    
    def save_conversation(self, filename: str) -> None:
        """Save conversation history to a file."""
        with open(filename, 'w') as f:
            json.dump(self.conversation_history, f, indent=2)
        print(f"💾 Conversation saved to {filename}")
    
    def load_conversation(self, filename: str) -> None:
        """Load conversation history from a file."""
        try:
            with open(filename, 'r') as f:
                self.conversation_history = json.load(f)
            print(f"📂 Conversation loaded from {filename}")
        except FileNotFoundError:
            print(f"❌ File not found: {filename}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {filename}")
