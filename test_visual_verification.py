#!/usr/bin/env python3
"""
Test script for visual verification functionality
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


def test_visual_verification():
    """Test the visual verification system."""
    print("🧪 Testing Visual Verification System")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("📸 This test will take multiple screenshots to verify actions")
    print("🔍 The agent will analyze each screenshot to confirm success")
    print()
    
    # Test opening Calculator with visual verification
    print("🧮 Testing: open calculator")
    print("-" * 30)
    
    try:
        response = agent.chat("open calculator")
        print(f"🤖 Full Response:\n{response}")
        
        if "Visual Verification" in response:
            print("✅ Visual verification was performed!")
        else:
            print("⚠️  No visual verification in response")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("📸 Check the screenshots/ folder to see all captured images")
    print("🔍 The agent should have taken screenshots:")
    print("   • Before opening Calculator")
    print("   • Spotlight search results")
    print("   • After pressing Enter")
    print("   • Final state")
    
    # List recent screenshots
    screenshots_dir = Path("screenshots")
    if screenshots_dir.exists():
        recent_screenshots = sorted(screenshots_dir.glob("*.png"))[-5:]
        print(f"\n📁 Recent screenshots:")
        for screenshot in recent_screenshots:
            print(f"   • {screenshot.name}")
    
    print("\n🎯 Test complete!")


def test_current_state_awareness():
    """Test that agent takes screenshot before each action."""
    print("\n🧪 Testing Current State Awareness")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("📸 Testing that agent captures current state before actions...")
    
    # Simple test commands
    test_commands = [
        "take a screenshot",
        "what time is it?"
    ]
    
    for command in test_commands:
        print(f"\n📝 Testing: {command}")
        print("-" * 20)
        
        try:
            response = agent.chat(command)
            print(f"🤖 Response: {response[:100]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n✅ Current state awareness test complete!")


def main():
    """Run visual verification tests."""
    print("🚀 Mac AI Agent - Visual Verification Test")
    print("   Testing screenshot-based action verification")
    print()
    
    try:
        test_visual_verification()
        
        proceed = input("\n🤔 Test current state awareness? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            test_current_state_awareness()
        
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted!")
    
    print("\n🎯 The agent now:")
    print("   • 📸 Takes screenshots before every action")
    print("   • 📸 Takes screenshots during app opening process")
    print("   • 🔍 Uses LLM to analyze screenshots")
    print("   • ✅ Verifies if actions actually worked")
    print("\n   Try: python chat_agent.py")
    print("   Then: 'open calculator'")


if __name__ == "__main__":
    main()
