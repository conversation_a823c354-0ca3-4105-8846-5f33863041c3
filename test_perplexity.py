#!/usr/bin/env python3
"""
Test script for Perplexity integration
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent.perplexity_tool import PerplexityTool


def test_perplexity():
    """Test Perplexity integration."""
    print("🧪 Testing Perplexity Integration")
    print("=" * 50)
    
    # Create Perplexity tool
    perplexity = PerplexityTool()
    
    # Test connection
    print("🔗 Testing connection...")
    status = perplexity.test_connection()
    
    if status["success"]:
        print("✅ Perplexity connection successful!")
        print(f"   Test response: {status.get('test_response', 'N/A')}")
        
        # Test a simple question
        print("\n🤔 Testing real-time question...")
        result = perplexity.search_current_info("What is today's date?")
        
        if result:
            print("✅ Real-time query successful!")
            print(f"   Response: {result[:200]}...")
        else:
            print("❌ Real-time query failed")
            
    else:
        print(f"❌ Perplexity connection failed: {status['error']}")
        if "suggestion" in status:
            print(f"   Suggestion: {status['suggestion']}")
    
    print("\n" + "=" * 50)
    print("🎯 Test complete!")


if __name__ == "__main__":
    test_perplexity()
