#!/usr/bin/env python3
"""
Demo of time capabilities with Mac AI Agent
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demo_time_capabilities():
    """Demonstrate time-related capabilities."""
    print("🕐 Time Tool Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test time-related questions
    time_questions = [
        "What time is it?",
        "What's the current date?",
        "What time is it in New York?",
        "Show me the world clock",
        "What's the time in Tokyo?",
        "What day is today?"
    ]
    
    print("🤖 Testing time-related queries...\n")
    
    for i, question in enumerate(time_questions, 1):
        print(f"📝 Test {i}: {question}")
        print("-" * 40)
        
        try:
            response = agent.chat(question)
            print(f"🤖 Response: {response}")
            
            if "Time Info" in response:
                print("✅ Successfully used time tool")
            else:
                print("⚠️  Response may not include time data")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        time.sleep(1)  # Brief pause between requests
    
    print("🎉 Time demo complete!")


def demo_direct_time_tool():
    """Demonstrate direct time tool usage."""
    print("\n" + "=" * 50)
    print("🔧 Direct Time Tool Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    time_tool = agent.time_tool
    
    print("📍 Current time (East Africa Time - default):")
    current = time_tool.get_current_time()
    print(time_tool.format_time_response(current))
    
    print("\n🌍 World Clock:")
    world_clock = time_tool.get_world_clock()
    print(time_tool.format_time_response(world_clock))
    
    print("\n🕐 Specific timezone queries:")
    timezones = ["America/New_York", "Europe/London", "Asia/Tokyo", "UTC"]
    
    for tz in timezones:
        tz_time = time_tool.get_current_time(tz)
        if tz_time["success"]:
            print(f"• {tz}: {tz_time['formatted']}")
        else:
            print(f"• {tz}: Error - {tz_time.get('error', 'Unknown')}")
    
    print("\n✅ Direct time tool demo complete!")


def demo_time_commands():
    """Demonstrate terminal time commands."""
    print("\n" + "=" * 50)
    print("⌨️  Terminal Time Commands Demo")
    print("=" * 50)
    
    print("Available time commands in the terminal:")
    print("• 'time' - Show current time (East Africa Time)")
    print("• 'worldclock' - Show world clock")
    print("• 'status' - Shows current time in status")
    print("\nTry these commands when you run: python chat_agent.py")


def main():
    """Run the time capabilities demo."""
    print("🚀 Mac AI Agent - Time Capabilities Demo")
    print("   Default timezone: East Africa Time (UTC+3)")
    print()
    
    try:
        demo_time_capabilities()
        
        proceed = input("\n🤔 Continue with direct time tool demo? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_direct_time_tool()
        
        proceed = input("\n🤔 Show terminal commands info? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_time_commands()
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted!")
    
    print("\n🎯 Try the full experience with: python chat_agent.py")
    print("   Ask questions like:")
    print("   • 'What time is it?'")
    print("   • 'What time is it in London?'")
    print("   • 'Show me the world clock'")


if __name__ == "__main__":
    main()
