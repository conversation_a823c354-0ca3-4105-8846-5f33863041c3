#!/usr/bin/env python3
"""
Basic usage examples for Mac AI Agent
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demo_screenshot():
    """Demonstrate screenshot functionality."""
    print("📸 Screenshot Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    print("Taking a screenshot...")
    screenshot_path = agent.take_screenshot_with_timestamp()
    print(f"Screenshot saved: {screenshot_path}")
    
    print("✅ Screenshot demo complete!\n")


def demo_spotlight_search():
    """Demonstrate Spotlight search."""
    print("🔍 Spotlight Search Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    print("Opening Spotlight search...")
    agent.spotlight_search("Calculator")
    
    print("⚠️  Spotlight should now be open with 'Calculator' typed.")
    print("   Press Enter to open Calculator, or Esc to cancel.")
    print("✅ Spotlight demo complete!\n")


def demo_app_switching():
    """Demonstrate app switching."""
    print("🔄 App Switching Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    print("Using Cmd+Tab to switch apps...")
    agent.cmd_tab_switch(1)
    
    print("⚠️  App switcher should now be visible.")
    print("   Release to switch to the selected app.")
    print("✅ App switching demo complete!\n")


def demo_typing():
    """Demonstrate typing functionality."""
    print("⌨️  Typing Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    print("⚠️  This will type text wherever your cursor is!")
    proceed = input("Make sure you have a text field selected. Continue? (y/n): ")
    
    if proceed.lower() in ['y', 'yes']:
        print("Typing demo text...")
        agent.screen.type_text("Hello from Mac AI Agent! 🤖")
        print("✅ Typing demo complete!")
    else:
        print("Typing demo skipped.")
    
    print()


def demo_mouse_operations():
    """Demonstrate mouse operations."""
    print("🖱️  Mouse Operations Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    # Get current mouse position
    x, y = agent.screen.get_mouse_position()
    print(f"Current mouse position: ({x}, {y})")
    
    print("Moving mouse to center of screen...")
    center_x = agent.screen.screen_width // 2
    center_y = agent.screen.screen_height // 2
    agent.screen.move_mouse(center_x, center_y, duration=1.0)
    
    print(f"Mouse moved to: ({center_x}, {center_y})")
    
    # Move back to original position
    print("Moving mouse back to original position...")
    agent.screen.move_mouse(x, y, duration=1.0)
    
    print("✅ Mouse operations demo complete!\n")


def demo_chat_interface():
    """Demonstrate the chat interface."""
    print("💬 Chat Interface Demo")
    print("-" * 30)
    
    agent = MacAIAgent()
    
    # Check LM Studio status
    status = agent.get_status()
    if status['lm_studio']['running']:
        print("✅ LM Studio is running - full chat functionality available")
        
        # Demo conversation
        test_messages = [
            "Hello! Can you help me?",
            "What can you do on my Mac?",
            "How do I take a screenshot?"
        ]
        
        for message in test_messages:
            print(f"\n👤 User: {message}")
            response = agent.chat(message)
            print(f"🤖 Agent: {response}")
            time.sleep(1)
    else:
        print("⚠️  LM Studio not running - limited functionality")
        print("   Start LM Studio with a model for full chat capabilities")
    
    print("✅ Chat interface demo complete!\n")


def main():
    """Run all demos."""
    print("🤖 Mac AI Agent - Basic Usage Examples")
    print("=" * 50)
    print()
    
    demos = [
        ("Screenshot", demo_screenshot),
        ("Spotlight Search", demo_spotlight_search),
        ("App Switching", demo_app_switching),
        ("Typing", demo_typing),
        ("Mouse Operations", demo_mouse_operations),
        ("Chat Interface", demo_chat_interface)
    ]
    
    print("Available demos:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"  {i}. {name}")
    print("  0. Run all demos")
    print()
    
    try:
        choice = input("Select demo (0-6): ").strip()
        
        if choice == "0":
            # Run all demos
            for name, demo_func in demos:
                print(f"\n{'='*20} {name} {'='*20}")
                demo_func()
                input("Press Enter to continue to next demo...")
        
        elif choice.isdigit() and 1 <= int(choice) <= len(demos):
            # Run specific demo
            name, demo_func = demos[int(choice) - 1]
            print(f"\n{'='*20} {name} {'='*20}")
            demo_func()
        
        else:
            print("Invalid choice!")
            return
    
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted!")
        return
    
    print("🎉 Demo complete! Try running 'python chat_agent.py' for the full experience.")


if __name__ == "__main__":
    main()
