#!/usr/bin/env python3
"""
Demo of working automation capabilities
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demo_app_opening():
    """Demonstrate opening applications."""
    print("🚀 Application Opening Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    apps_to_test = [
        "calculator",
        "safari", 
        "notes"
    ]
    
    print("🤖 Testing application opening...\n")
    
    for app in apps_to_test:
        print(f"📱 Opening {app}...")
        try:
            response = agent.chat(f"open {app}")
            print(f"🤖 Response: {response}")
            
            if "✅" in response:
                print(f"✅ {app} opened successfully!")
            else:
                print(f"⚠️  Issue opening {app}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        time.sleep(2)  # Brief pause between apps
    
    print("🎉 App opening demo complete!")


def demo_automation_commands():
    """Demonstrate various automation commands."""
    print("\n" + "=" * 50)
    print("⚡ Automation Commands Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    commands = [
        "take a screenshot",
        "switch to next app",
        "open calculator"
    ]
    
    for command in commands:
        print(f"⚡ Executing: {command}")
        print("-" * 30)
        
        try:
            response = agent.chat(command)
            print(f"🤖 Response: {response}")
            
            if "✅" in response:
                print("✅ Command executed successfully")
            elif "❌" in response:
                print("⚠️  Command had issues")
            else:
                print("ℹ️  Command processed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        time.sleep(1)
    
    print("✅ Automation commands demo complete!")


def demo_supported_apps():
    """Show supported applications."""
    print("\n" + "=" * 50)
    print("📱 Supported Applications")
    print("=" * 50)
    
    supported_apps = {
        "Browsers": ["chrome", "safari", "firefox"],
        "Productivity": ["calculator", "notes", "mail", "calendar"],
        "Development": ["vscode", "code", "terminal", "xcode"],
        "Communication": ["slack", "discord", "zoom", "teams"],
        "Media": ["spotify"],
        "System": ["finder"]
    }
    
    for category, apps in supported_apps.items():
        print(f"\n🏷️  {category}:")
        for app in apps:
            print(f"   • {app}")
    
    print(f"\n💡 Usage: Just say 'open [app_name]'")
    print(f"   Example: 'open chrome' → Opens Google Chrome")


def demo_direct_vs_planned():
    """Demonstrate direct execution vs planned execution."""
    print("\n" + "=" * 50)
    print("🎯 Direct vs Planned Execution")
    print("=" * 50)
    
    print("✅ **Direct Execution** (immediate):")
    print("   • 'open chrome' → ✅ Successfully opened Google Chrome")
    print("   • 'take a screenshot' → ✅ Screenshot saved to...")
    print("   • 'switch to next app' → ✅ Switched to next application...")
    print()
    
    print("🤔 **Planned Execution** (requires confirmation):")
    print("   • Complex multi-step tasks")
    print("   • Custom coordinates clicking")
    print("   • Advanced automation workflows")
    print()
    
    print("💡 The agent automatically chooses the best approach!")


def main():
    """Run the automation demo."""
    print("🚀 Mac AI Agent - Working Automation Demo")
    print("   Demonstrating actual automation capabilities")
    print()
    
    try:
        demo_app_opening()
        
        proceed = input("\n🤔 Continue with automation commands demo? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_automation_commands()
        
        proceed = input("\n🤔 Show supported apps list? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_supported_apps()
        
        proceed = input("\n🤔 Show execution types info? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_direct_vs_planned()
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted!")
    
    print("\n🎯 Ready to use! Try:")
    print("   python chat_agent.py")
    print("   Then say: 'open chrome'")
    print("   Chrome will open automatically! 🚀")


if __name__ == "__main__":
    main()
