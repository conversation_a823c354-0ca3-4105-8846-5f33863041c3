#!/usr/bin/env python3
"""
Demo of Perplexity integration with Mac AI Agent
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demo_real_time_info():
    """Demonstrate real-time information capabilities."""
    print("🌐 Perplexity Integration Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test questions that should trigger Perplexity
    test_questions = [
        "What's the weather like today?",
        "What are the latest news about AI?",
        "What's Apple's current stock price?",
        "What are the current events happening today?",
        "What's the latest news about Tesla?"
    ]
    
    print("🤖 Testing real-time information queries...\n")
    
    for i, question in enumerate(test_questions, 1):
        print(f"📝 Test {i}: {question}")
        print("-" * 40)
        
        try:
            response = agent.chat(question)
            print(f"🤖 Response: {response[:200]}...")
            
            if "Real-time Info" in response:
                print("✅ Successfully used Perplexity for real-time data")
            else:
                print("⚠️  Response may not include real-time data")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        time.sleep(2)  # Brief pause between requests
    
    print("🎉 Demo complete!")
    print("\nThe agent can now:")
    print("• 🌐 Get real-time information from Perplexity")
    print("• 🧠 Combine it with local LLM intelligence")
    print("• 🖱️ Perform Mac automation tasks")
    print("• 💬 Chat naturally about current events")


def demo_mixed_capabilities():
    """Demonstrate mixing automation with real-time info."""
    print("\n" + "=" * 50)
    print("🔄 Mixed Capabilities Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    mixed_requests = [
        "Take a screenshot and tell me what's the latest news about Apple",
        "What's the current weather? I might want to open a weather app",
        "Get me the latest AI news, then help me open Safari to read more"
    ]
    
    for i, request in enumerate(mixed_requests, 1):
        print(f"📝 Mixed Request {i}: {request}")
        print("-" * 40)
        
        try:
            response = agent.chat(request)
            print(f"🤖 Response: {response[:300]}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        time.sleep(2)
    
    print("✅ Mixed capabilities demo complete!")


def main():
    """Run the Perplexity integration demo."""
    print("🚀 Mac AI Agent with Perplexity Integration")
    print("   Real-time information + Mac automation")
    print()
    
    try:
        demo_real_time_info()
        
        proceed = input("\n🤔 Continue with mixed capabilities demo? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demo_mixed_capabilities()
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted!")
    
    print("\n🎯 Try the full experience with: python chat_agent.py")


if __name__ == "__main__":
    main()
