# Mac AI Agent

An intelligent automation agent for macOS that can interact with your screen through clicking, dragging, taking screenshots, typing, and using keyboard shortcuts.

## Features

- **Screen Interaction**: Click, drag, and interact with UI elements
- **Screenshot Capture**: Take and analyze screenshots
- **Keyboard Control**: Type text and use keyboard shortcuts (including Mac-specific ones like Cmd+Tab)
- **AI Vision**: Identify UI elements and text using computer vision
- **Mac Integration**: Native macOS support with system-level access

## Requirements

- macOS (tested on M1 Air)
- Python 3.8+
- Accessibility permissions for screen control

## Installation

1. Clone this repository
2. Create a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Setup

Before using the agent, you'll need to grant accessibility permissions:

1. Go to System Preferences → Security & Privacy → Privacy → Accessibility
2. Add your terminal application and Python to the list of allowed apps
3. Enable the checkboxes for both

## Quick Start

```python
from mac_ai_agent import MacAIAgent

# Create an agent instance
agent = MacAIAgent()

# Take a screenshot
screenshot = agent.take_screenshot()

# Click at coordinates
agent.click(100, 200)

# Type text
agent.type_text("Hello, World!")

# Use keyboard shortcuts
agent.key_combo("cmd", "tab")  # Switch applications
```

## Safety

This tool can control your computer. Use with caution and always test in a safe environment first.
