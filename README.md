# Mac AI Agent

An intelligent automation agent for macOS that can interact with your screen through clicking, dragging, taking screenshots, typing, and using keyboard shortcuts.

## Features

- **Screen Interaction**: Click, drag, and interact with UI elements
- **Screenshot Capture**: Take and analyze screenshots
- **Keyboard Control**: Type text and use keyboard shortcuts (including Mac-specific ones like Cmd+Tab)
- **AI Vision**: Identify UI elements and text using computer vision
- **Mac Integration**: Native macOS support with system-level access

## Requirements

- macOS (tested on M1 Air)
- Python 3.8+
- Accessibility permissions for screen control

## Installation

### Quick Setup
Run the setup script for guided installation:
```bash
python setup_agent.py
```

### Manual Setup
1. Create a virtual environment:
   ```bash
   # Using conda (recommended)
   conda create -n mac-ai-agent python=3.10
   conda activate mac-ai-agent

   # Or using venv
   python3 -m venv venv
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Setup

### 1. Accessibility Permissions
Grant accessibility permissions for screen automation:

1. Go to **System Preferences → Security & Privacy → Privacy → Accessibility**
2. Click the lock icon and enter your password
3. Add your terminal application (Terminal.app or iTerm.app)
4. Add Python (find your Python executable path)
5. Enable checkboxes for both applications

### 2. LM Studio Setup (Recommended)
For the AI brain functionality:

1. Download [LM Studio](https://lmstudio.ai/)
2. Install and open LM Studio
3. Download a model (recommended: Llama 2 7B or similar)
4. Load the model in LM Studio
5. Start the local server (usually http://localhost:1234)

The agent works with limited functionality without LM Studio, but you'll get the best experience with a local LLM.

## Usage

### Terminal Chat Interface (Recommended)
Start the interactive chat agent:
```bash
python chat_agent.py
```

The agent will:
- 🤖 Chat with you using natural language
- 📸 Take screenshots when needed
- 🔍 Help you search and open applications
- ⌨️ Execute keyboard shortcuts and typing
- 🖱️ Perform mouse clicks and drags
- 🧠 Use local LLM (via LM Studio) for intelligent responses

### Example Conversations
```
👤 You: open Safari
🤖 Agent: I'll help you open Safari using Spotlight search...

👤 You: take a screenshot and tell me what you see
🤖 Agent: Taking a screenshot... [analyzes screen content]

👤 You: switch to the next app
🤖 Agent: Using Cmd+Tab to switch applications...
```

### Programmatic Usage
```python
from mac_ai_agent import MacAIAgent

# Create an agent instance
agent = MacAIAgent()

# Take a screenshot
screenshot_path = agent.take_screenshot_with_timestamp()

# Chat with the agent
response = agent.chat("open Calculator app")

# Direct screen interactions
agent.screen.click(100, 200)
agent.screen.type_text("Hello, World!")
agent.screen.key_combo("cmd", "tab")
```

### Examples
Run the example demos:
```bash
python examples/basic_usage.py
```

## Safety & Best Practices

⚠️ **Important Safety Information**

This tool can control your computer. Please follow these safety guidelines:

### Before Using
- **Test in a safe environment** - Try on a test machine or with non-critical applications first
- **Save your work** - Close or save important documents before running automation
- **Understand the commands** - Review what the agent plans to do before approving actions
- **Keep PyAutoGUI failsafe enabled** - Move mouse to top-left corner to emergency stop

### Built-in Safety Features
- **Action confirmation** - Agent shows you the plan before executing
- **Screenshot logging** - All screenshots are saved for review
- **Graceful interruption** - Ctrl+C stops the agent safely
- **Limited scope** - Agent only performs actions you explicitly approve

### Recommended Practices
1. Start with simple commands like "take a screenshot"
2. Use the chat interface rather than direct automation calls
3. Keep LM Studio running for intelligent decision-making
4. Review the conversation history periodically
5. Test new commands in a controlled environment

### Troubleshooting
- **"Permission denied"** → Check accessibility permissions
- **"LM Studio not found"** → Start LM Studio and load a model
- **"Agent not responding"** → Check terminal for error messages
- **"Actions not working"** → Verify screen coordinates and timing

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- Built with PyAutoGUI for screen automation
- Powered by LM Studio for local LLM integration
- Designed for macOS with native system integration
