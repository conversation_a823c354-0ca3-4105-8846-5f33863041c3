#!/usr/bin/env python3
"""
Complete system test for Mac AI Agent with visual verification
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


def test_complete_workflow():
    """Test the complete workflow with visual verification."""
    print("🚀 Complete Mac AI Agent Test")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("🧪 This test demonstrates the complete workflow:")
    print("   • 📸 Screenshots before every action")
    print("   • 🔍 Visual verification with LLM analysis")
    print("   • ✅ Confirmation of action success")
    print()
    
    # Test sequence
    test_commands = [
        "open calculator",
        "take a screenshot", 
        "what time is it?"
    ]
    
    for i, command in enumerate(test_commands, 1):
        print(f"📝 Test {i}: {command}")
        print("-" * 40)
        
        try:
            response = agent.chat(command)
            print(f"🤖 Response: {response[:200]}...")
            
            if "Visual Verification" in response:
                print("✅ Visual verification performed!")
            elif "📸" in response:
                print("✅ Screenshots captured!")
            else:
                print("ℹ️  Command processed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
    
    # Show recent screenshots
    screenshots_dir = Path("screenshots")
    if screenshots_dir.exists():
        recent_screenshots = sorted(screenshots_dir.glob("*.png"))[-10:]
        print(f"📁 Recent screenshots ({len(recent_screenshots)}):")
        for screenshot in recent_screenshots:
            print(f"   • {screenshot.name}")
    
    print("\n🎉 Complete system test finished!")


def demonstrate_visual_verification():
    """Demonstrate the visual verification capabilities."""
    print("\n" + "=" * 50)
    print("👁️ Visual Verification Demonstration")
    print("=" * 50)
    
    print("🔍 **How Visual Verification Works:**")
    print()
    print("1. **Before Action**: Agent takes screenshot of current state")
    print("2. **During Action**: Screenshots captured during process (e.g., Spotlight)")
    print("3. **After Action**: Screenshot taken after action completion")
    print("4. **LLM Analysis**: Local LLM analyzes all screenshots")
    print("5. **Verification Report**: Agent reports if action succeeded")
    print()
    
    print("📸 **Screenshot Types Captured:**")
    print("   • Current state (before every chat message)")
    print("   • Before action (baseline)")
    print("   • Spotlight search results")
    print("   • After action execution")
    print("   • Final state verification")
    print()
    
    print("🧠 **LLM Analysis Includes:**")
    print("   • Did the action appear successful?")
    print("   • What applications/windows are visible?")
    print("   • Any issues or problems detected?")
    print("   • Specific observations about screen content")
    print()
    
    print("✅ **Benefits:**")
    print("   • Confirms actions actually worked")
    print("   • Provides visual feedback to user")
    print("   • Creates audit trail of all actions")
    print("   • Enables debugging when things go wrong")


def show_example_workflow():
    """Show example of the complete workflow."""
    print("\n" + "=" * 50)
    print("📋 Example Workflow")
    print("=" * 50)
    
    print("👤 **User**: 'open calculator'")
    print()
    print("🤖 **Agent Process**:")
    print("   1. 📸 Takes screenshot of current state")
    print("   2. 📸 Takes screenshot before opening Calculator")
    print("   3. 🔍 Opens Spotlight search")
    print("   4. 📸 Takes screenshot of Spotlight results")
    print("   5. ⌨️  Types 'Calculator' and presses Enter")
    print("   6. 📸 Takes screenshot after pressing Enter")
    print("   7. ⏱️  Waits for app to load")
    print("   8. 📸 Takes final screenshot")
    print("   9. 🧠 Sends all screenshots to LLM for analysis")
    print("   10. 🔍 LLM analyzes if Calculator actually opened")
    print("   11. ✅ Reports success/failure with visual evidence")
    print()
    print("🤖 **Agent Response**:")
    print("   ✅ Attempted to open Calculator - check screenshots for verification")
    print("   🔍 Visual Verification: Calculator app is now visible on screen...")


def main():
    """Run the complete system test."""
    print("🚀 Mac AI Agent - Complete System Test")
    print("   Testing visual verification and action confirmation")
    print()
    
    try:
        test_complete_workflow()
        
        proceed = input("\n🤔 Show visual verification explanation? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            demonstrate_visual_verification()
        
        proceed = input("\n🤔 Show example workflow? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            show_example_workflow()
        
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted!")
    
    print("\n🎯 **Your Agent Now Has:**")
    print("   • 👁️ Visual awareness of screen state")
    print("   • 📸 Automatic screenshot documentation")
    print("   • 🔍 LLM-powered action verification")
    print("   • ✅ Confirmation that actions actually worked")
    print("   • 🧠 Intelligence from Phi-3 + Perplexity + Time awareness")
    print()
    print("   **Ready to use**: python chat_agent.py")
    print("   **Try**: 'open chrome' - it will verify Chrome actually opens!")


if __name__ == "__main__":
    main()
