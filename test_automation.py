#!/usr/bin/env python3
"""
Test script for automation functionality
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


def test_open_app():
    """Test opening applications."""
    print("🧪 Testing Application Opening")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test the direct execution method
    print("🔧 Testing direct execution method...")
    result = agent.execute_simple_action("open calculator")
    
    if result:
        print(f"✅ Direct execution result: {result['message']}")
        if result['success']:
            print("✅ Calculator should be opening!")
        else:
            print("❌ Failed to open Calculator")
    else:
        print("❌ No direct execution result")
    
    print("\n" + "=" * 50)
    print("🎯 Test complete!")
    print("\nTo test 'open Chrome', run:")
    print("python chat_agent.py")
    print("Then type: open chrome")


def test_chat_interface():
    """Test the chat interface with automation."""
    print("\n🧪 Testing Chat Interface Automation")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    test_commands = [
        "take a screenshot",
        "open calculator", 
        "switch to next app"
    ]
    
    for command in test_commands:
        print(f"\n📝 Testing: {command}")
        print("-" * 30)
        
        try:
            response = agent.chat(command)
            print(f"🤖 Response: {response}")
            
            if "✅" in response:
                print("✅ Command executed successfully")
            elif "❌" in response:
                print("⚠️  Command had issues")
            else:
                print("ℹ️  Command processed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n🎉 Chat interface test complete!")


def main():
    """Run automation tests."""
    print("🚀 Mac AI Agent - Automation Test")
    print("   Testing 'open Chrome' and other automation")
    print()
    
    try:
        test_open_app()
        
        proceed = input("\n🤔 Test chat interface automation? (y/n): ")
        if proceed.lower() in ['y', 'yes']:
            test_chat_interface()
        
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted!")
    
    print("\n🎯 To test 'open Chrome' fully:")
    print("   1. Run: python chat_agent.py")
    print("   2. Type: open chrome")
    print("   3. Chrome should open automatically!")


if __name__ == "__main__":
    main()
